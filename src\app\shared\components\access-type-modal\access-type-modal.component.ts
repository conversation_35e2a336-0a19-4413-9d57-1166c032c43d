import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccessType, AccessTypeSelection } from '../../../core/models/cart.model';

@Component({
  selector: 'app-access-type-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './access-type-modal.component.html',
  styleUrls: ['./access-type-modal.component.css']
})
export class AccessTypeModalComponent {
  @Input() isVisible = false;
  @Input() selection: AccessTypeSelection | null = null;
  @Output() accessTypeSelected = new EventEmitter<'oneday' | 'subscription'>();
  @Output() modalClosed = new EventEmitter<void>();

  selectedAccessType: 'oneday' | 'subscription' | null = null;

  accessTypes: AccessType[] = [
    {
      id: 'oneday',
      name: 'Доступ на 1 день',
      description: 'Получите доступ к игре на 24 часа',
      duration: '24 часа',
      icon: 'clock'
    },
    {
      id: 'subscription',
      name: 'Подписка на 30 дней',
      description: 'Полный доступ к игре на целый месяц',
      duration: '30 дней',
      icon: 'calendar'
    }
  ];

  selectAccessType(accessType: 'oneday' | 'subscription'): void {
    this.selectedAccessType = accessType;
  }

  confirmSelection(): void {
    if (this.selectedAccessType) {
      this.accessTypeSelected.emit(this.selectedAccessType);
      this.closeModal();
    }
  }

  closeModal(): void {
    this.selectedAccessType = null;
    this.modalClosed.emit();
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  getAccessTypePrice(accessType: 'oneday' | 'subscription'): string {
    if (!this.selection) return '0';

    const basePrice = parseFloat(this.selection.price);

    // Calculate price based on access type
    if (accessType === 'oneday') {
      // 1 day access costs the full price
      return this.selection.price;
    } else {
      // 30 day subscription costs the full price (same as 1 day for now)
      // You can adjust this logic based on your pricing strategy
      return this.selection.price;
    }
  }
}
