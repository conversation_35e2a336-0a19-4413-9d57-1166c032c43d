/* Package Purchase Modal Styles */

/* Modal backdrop - prevent scrolling and clicking through */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 80;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

/* Modal content container */
.modal-content-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Modal animations */
.modal-enter {
  animation: modalFadeIn 0.3s ease-out;
}

.modal-leave {
  animation: modalFadeOut 0.3s ease-in;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

/* Game selection cards */
.game-card {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.game-card:hover {
  background-color: rgba(71, 85, 105, 0.4);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.game-card.selected {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

/* Game image container */
.game-image-container {
  aspect-ratio: 4/3;
  height: 120px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

.game-image {
  transition: transform 0.3s ease;
}

.game-card:hover .game-image {
  transform: scale(1.05);
}

/* Selection overlay animation */
.selection-overlay {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Game title styling */
.game-title {
  font-weight: 500;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Checkbox styling */
input[type="checkbox"] {
  width: 18px;
  height: 18px;
}

input[type="checkbox"]:checked {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Button hover effects */
button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Loading spinner */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Package info styling */
.package-info {
  background: linear-gradient(135deg, rgba(71, 85, 105, 0.3) 0%, rgba(51, 65, 85, 0.3) 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
}

/* Preview section styling */
.preview-section {
  background: linear-gradient(135deg, rgba(71, 85, 105, 0.3) 0%, rgba(51, 65, 85, 0.3) 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
}

/* Error and success message styling */
.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
}

.success-message {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #86efac;
}

.info-message {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #93c5fd;
}

/* Benefits list styling */
.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.benefit-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  color: #4ade80;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal-content-container {
    padding: 0.5rem;
  }

  .game-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .package-info-grid {
    grid-template-columns: 1fr;
  }

  /* Smaller game images on mobile */
  .game-image-container {
    height: 100px;
  }
}

@media (max-width: 1024px) {
  .game-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Focus states for accessibility */
input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Selection indicator animation */
.selection-indicator {
  transition: all 0.2s ease;
}

/* Hover effects for better UX */
.game-selection-card:hover .selection-indicator {
  transform: scale(1.1);
}

.game-selection-card .selection-indicator {
  transition: transform 0.2s ease;
}

/* Dark theme adjustments */
.dark-theme {
  --modal-bg: rgba(15, 23, 42, 0.95);
  --modal-border: rgba(71, 85, 105, 0.5);
  --card-bg: rgba(51, 65, 85, 0.5);
  --card-border: rgba(71, 85, 105, 0.5);
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
}
