<!-- Package Purchase Modal -->
<div
  *ngIf="isVisible"
  class="modal-backdrop"
>
  <div class="modal-content-container">
    <!-- Modal Content -->
    <div 
      class="relative bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-600/50 rounded-2xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-hidden modal-enter"
      (click)="$event.stopPropagation()"
    >
      <!-- Header -->
      <div class="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-slate-600/50 p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-xl font-bold text-white mb-1">
              <span *ngIf="currentStep === 'game-selection'">Выбор игр</span>
              <span *ngIf="currentStep === 'payment'">Оплата</span>
            </h3>
            <p class="text-gray-300 text-sm" *ngIf="package">{{ package.name }}</p>
          </div>
          <button
            (click)="closeModal()"
            class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-slate-700/50 rounded-lg"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Progress Steps -->
        <div class="flex items-center mt-4 space-x-4">
          <!-- Game Selection Step -->
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                 [class]="currentStep === 'game-selection' ? 'bg-blue-600 text-white' :
                          currentStep === 'payment' ? 'bg-green-600 text-white' : 'bg-slate-600 text-gray-400'">
              1
            </div>
            <span class="ml-2 text-sm text-gray-300">Выбор игр</span>
          </div>
          <div class="flex-1 h-px bg-slate-600"></div>

          <!-- Payment Step -->
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                 [class]="currentStep === 'payment' ? 'bg-blue-600 text-white' : 'bg-slate-600 text-gray-400'">
              2
            </div>
            <span class="ml-2 text-sm text-gray-300">Оплата</span>
          </div>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="p-6 overflow-y-auto max-h-[60vh]">

        <!-- Loading State -->
        <div *ngIf="packageLoading" class="text-center py-8">
          <div class="flex items-center justify-center space-x-2">
            <svg class="animate-spin h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-gray-300">Загрузка пакета...</span>
          </div>
        </div>

        <!-- Error State -->
        <div *ngIf="packageError" class="text-center py-8">
          <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-4">
            <p class="text-red-300">{{ packageError }}</p>
          </div>
        </div>

        <!-- Game Selection Step -->
        <div *ngIf="currentStep === 'game-selection' && package && !packageLoading">
          
          <!-- Package Info -->
          <div class="bg-slate-700/30 rounded-lg p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 class="text-lg font-semibold text-white mb-2">{{ package.name }}</h4>
                <p class="text-gray-300 mb-4">{{ package.description }}</p>

                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-400">Цена:</span>
                    <span class="text-white font-medium">{{ formatPrice(package.price || '0') }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-400">Длительность:</span>
                    <span class="text-white">{{ package.duration_days || 0 }} дней</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-400">Максимум игр:</span>
                    <span class="text-white">{{ package.max_selectable_games || 0 }}</span>
                  </div>
                </div>
              </div>
              
              <div class="space-y-3">
                <div class="flex items-center space-x-2">
                  <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-gray-300 text-sm">{{ package.benefit_1 }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-gray-300 text-sm">{{ package.benefit_2 }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-gray-300 text-sm">{{ package.benefit_3 }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Game Selection -->
          <div class="mb-4">
            <p class="text-gray-300 text-sm mb-2">
              Выберите до {{ package.max_selectable_games || 0 }} игр из пакета:
            </p>
            <p class="text-blue-400 text-sm">
              Выбрано: {{ selectedGameIds.length }} / {{ package.max_selectable_games || 0 }}
            </p>
          </div>

          <!-- Error Message -->
          <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3 mb-4">
            <p class="text-red-300 text-sm">{{ errorMessage }}</p>
          </div>

          <div class="grid grid-cols-3 lg:grid-cols-4 gap-3 max-h-96 overflow-y-auto mb-4">
            <label *ngFor="let game of (package.games || [])"
                   class="game-card relative bg-slate-700/30 rounded-lg cursor-pointer overflow-hidden"
                   [class.selected]="isGameSelected(game.id)">

              <!-- Game Image -->
              <div class="game-image-container">
                <img
                  *ngIf="game.cover_image"
                  [src]="game.cover_image"
                  [alt]="game.title"
                  class="game-image w-full h-full object-cover"
                  (error)="onImageError($event)"
                >
                <div
                  *ngIf="!game.cover_image"
                  class="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-700 to-slate-800">
                  <svg class="w-8 h-8 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>

                <!-- Selection Overlay -->
                <div
                  *ngIf="isGameSelected(game.id)"
                  class="selection-overlay absolute inset-0 bg-blue-600/20 flex items-center justify-center">
                  <div class="bg-blue-600 rounded-full p-2 shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Game Title -->
              <div class="p-3">
                <h4 class="game-title text-white text-sm font-medium">{{ game.title }}</h4>
              </div>

              <!-- Hidden Checkbox -->
              <input
                type="checkbox"
                [checked]="isGameSelected(game.id)"
                (change)="onGameSelectionChange(game.id, $event)"
                class="absolute opacity-0 w-full h-full cursor-pointer"
              >
            </label>
          </div>

          <!-- Preview Section -->
          <div *ngIf="selectedGameIds.length > 0" class="mt-4">
            <div *ngIf="previewLoading" class="bg-slate-700/30 rounded-lg p-4 text-center">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="animate-spin h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-gray-300 text-sm">Обновление предварительного просмотра...</span>
                </div>
              </div>

            <div *ngIf="!previewLoading && previewData" class="bg-slate-700/30 rounded-lg p-4">
                <h4 class="text-white font-medium mb-3">Предварительный просмотр покупки</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-300">Выбрано игр:</span>
                    <span class="text-white">{{ previewData.preview.games_count }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-300">Длительность:</span>
                    <span class="text-white">{{ previewData.preview.expires_in_days }} дней</span>
                  </div>
                  <div class="flex justify-between font-medium">
                    <span class="text-gray-300">Стоимость:</span>
                    <span class="text-white">{{ formatPrice(previewData.preview.total_price) }}</span>
                  </div>
                </div>
                
                <div *ngIf="!previewData.can_purchase" class="mt-3 bg-red-500/20 border border-red-500/50 rounded p-2">
                  <p class="text-red-300 text-xs">Покупка недоступна. Проверьте выбранные игры.</p>
                </div>


              </div>
            </div>
          </div>
        </div>

        <!-- Payment Step -->
        <div *ngIf="currentStep === 'payment' && package && !packageLoading">

          <div class="space-y-6">
            <!-- Subscription Connection Header -->
            <div class="text-center">
              <h3 class="text-2xl font-bold text-white mb-6">Подключение подписки</h3>
            </div>

            <!-- Subscription Details -->
            <div class="bg-slate-700/30 rounded-lg p-6 space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-gray-300 text-lg">Тариф:</span>
                <span class="text-white text-lg font-semibold">{{ package.name }}</span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-gray-300 text-lg">Стоимость:</span>
                <span class="text-white text-lg font-semibold">{{ formatPrice(previewData?.preview?.total_price || package.price) }} / месяц</span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-gray-300 text-lg">Действует до:</span>
                <span class="text-white text-lg font-semibold">{{ getExpirationDate() }}</span>
              </div>

              <div class="border-t border-slate-600 pt-4">
                <div class="flex justify-between items-center">
                  <span class="text-gray-300 text-lg">Способ оплаты:</span>
                  <span class="text-white text-lg font-semibold">Kaspi QR</span>
                </div>
              </div>
            </div>

            <!-- Payment Instructions -->
            <div class="bg-blue-600/10 border border-blue-600/30 rounded-lg p-6 text-center">
              <p class="text-white text-lg mb-4">Перейдите в приложение Kaspi и отсканируйте код</p>

              <!-- QR Code Placeholder -->
              <div class="bg-white rounded-lg p-8 mx-auto max-w-xs">
                <div class="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
                  <div class="text-center">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5v5H4V4zm11 0h5v5h-5V4zM4 15h5v5H4v-5z"></path>
                    </svg>
                    <p class="text-gray-600 text-sm font-medium">Здесь будет QR код</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="border-t border-slate-600/50 p-6 bg-slate-800/50">
        <!-- Game Selection Step Footer -->
        <div *ngIf="currentStep === 'game-selection'" class="flex justify-between items-center">
          <button
            (click)="closeModal()"
            class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors text-sm"
          >
            Отмена
          </button>

          <button
            (click)="goToPayment()"
            [disabled]="!canProceedToPayment()"
            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
          >
            Продолжить к оплате
            <span *ngIf="purchaseLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Обработка...
            </span>
          </button>
        </div>

        <!-- Payment Step Footer -->
        <div *ngIf="currentStep === 'payment'" class="flex justify-between items-center">
          <button
            (click)="goBackToGameSelection()"
            class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors text-sm"
          >
            Назад
          </button>

          <button
            (click)="completePurchase()"
            [disabled]="!canPurchase() || purchaseLoading"
            class="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
          >
            <span *ngIf="!purchaseLoading">Оплатить {{ formatPrice(previewData?.preview?.total_price || package?.price || '0') }}</span>

          </button>
        </div>
      </div>
    </div>
  </div>
