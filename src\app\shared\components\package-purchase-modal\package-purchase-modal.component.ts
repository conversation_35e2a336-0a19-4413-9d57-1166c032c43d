import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, OnChanges } from '@angular/core';
import { Subscription } from 'rxjs';
import { GamePackageService } from '../../../core/services/game-package.service';
import { ModalService } from '../../../core/services/modal.service';
import { UserSummaryService } from '../../../core/services/user-summary.service';
import { GamePackage, GamePackageGame, PackagePreviewResponse } from '../../../core/models/game-package.model';

@Component({
  selector: 'app-package-purchase-modal',
  standalone: false,
  templateUrl: './package-purchase-modal.component.html',
  styleUrl: './package-purchase-modal.component.css'
})
export class PackagePurchaseModalComponent implements OnInit, OnDestroy, OnChanges {
  @Input() isVisible = false;
  @Input() packageId: number | null = null;
  @Output() modalClosed = new EventEmitter<void>();
  @Output() purchaseCompleted = new EventEmitter<void>();

  // Step management
  currentStep: 'game-selection' | 'payment' = 'game-selection';

  // Package data
  package: GamePackage | null = null;
  packageLoading = false;
  packageError = '';

  // Game selection
  selectedGameIds: number[] = [];

  // Preview data
  previewData: PackagePreviewResponse | null = null;
  previewLoading = false;

  // Purchase state
  purchaseLoading = false;
  errorMessage = '';

  private subscription?: Subscription;

  constructor(
    private gamePackageService: GamePackageService,
    private modalService: ModalService,
    private userSummaryService: UserSummaryService
  ) {}

  ngOnInit(): void {
    if (this.packageId) {
      this.loadPackage();
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
    this.enableBodyScroll();
  }

  ngOnChanges(): void {
    if (this.isVisible) {
      this.disableBodyScroll();
      if (this.packageId) {
        // Always load package when packageId is provided and modal is visible
        // This ensures we load the correct package even if switching between different packages
        this.loadPackage();
      }
    } else {
      this.enableBodyScroll();
    }
  }

  loadPackage(): void {
    if (!this.packageId) return;

    this.packageLoading = true;
    this.packageError = '';

    this.gamePackageService.getGamePackage(this.packageId).subscribe({
      next: (packageData) => {
        this.package = packageData;
        this.packageLoading = false;
        this.selectedGameIds = [];
        this.previewData = null;
      },
      error: (error) => {
        this.packageError = error.message || 'Ошибка загрузки пакета';
        this.packageLoading = false;
      }
    });
  }

  closeModal(): void {
    this.enableBodyScroll();
    this.resetModal();
    this.modalClosed.emit();
  }

  private resetModal(): void {
    this.currentStep = 'game-selection';
    this.package = null;
    this.packageLoading = false;
    this.packageError = '';
    this.selectedGameIds = [];
    this.previewData = null;
    this.previewLoading = false;
    this.purchaseLoading = false;
    this.errorMessage = '';
  }

  // Step navigation methods
  goToPayment(): void {
    if (this.canProceedToPayment()) {
      this.currentStep = 'payment';
    }
  }

  goBackToGameSelection(): void {
    this.currentStep = 'game-selection';
  }

  canProceedToPayment(): boolean {
    return this.selectedGameIds.length === (this.package?.max_selectable_games || 0) &&
           this.previewData?.can_purchase === true;
  }

  getExpirationDate(): string {
    const today = new Date();
    const expirationDate = new Date(today);
    const durationDays = this.package?.duration_days || 30;
    expirationDate.setDate(today.getDate() + durationDays);

    const day = expirationDate.getDate().toString().padStart(2, '0');
    const month = (expirationDate.getMonth() + 1).toString().padStart(2, '0');
    const year = expirationDate.getFullYear().toString().slice(-2);

    return `${day}/${month}/${year}`;
  }

  // Game selection methods
  onGameSelectionChange(gameId: number, event: any): void {
    if (event.target.checked) {
      if (this.selectedGameIds.length < (this.package?.max_selectable_games || 0)) {
        this.selectedGameIds.push(gameId);
        this.updatePreview();
      } else {
        event.target.checked = false;
        this.modalService.error('Превышен лимит', `Вы можете выбрать максимум ${this.package?.max_selectable_games} игр`);
      }
    } else {
      this.selectedGameIds = this.selectedGameIds.filter(id => id !== gameId);
      this.updatePreview();
    }
  }

  isGameSelected(gameId: number): boolean {
    return this.selectedGameIds.includes(gameId);
  }

  onImageError(event: any): void {
    event.target.style.display = 'none';
  }

  private updatePreview(): void {
    if (!this.package || this.selectedGameIds.length === 0) {
      this.previewData = null;
      return;
    }

    this.previewLoading = true;
    this.errorMessage = '';

    this.gamePackageService.previewPackagePurchase(this.package.id, this.selectedGameIds).subscribe({
      next: (response) => {
        this.previewLoading = false;
        this.previewData = response;
        
        // Show warnings if any
        if (response.preview.warnings && response.preview.warnings.length > 0) {
          this.modalService.alert('Предупреждение', response.preview.warnings.join('\n'));
        }
      },
      error: (error) => {
        this.previewLoading = false;
        this.errorMessage = error.message || 'Ошибка при получении предварительного просмотра';
        this.previewData = null;
      }
    });
  }

  canPurchase(): boolean {
    return this.selectedGameIds.length === (this.package?.max_selectable_games || 0) && 
           this.previewData?.can_purchase === true;
  }

  completePurchase(): void {
    if (!this.package || !this.canPurchase()) {
      this.modalService.error('Ошибка', 'Выберите все необходимые игры для покупки');
      return;
    }

    this.purchaseLoading = true;
    this.errorMessage = '';

    this.gamePackageService.purchasePackageWithGames(this.package.id, this.selectedGameIds).subscribe({
      next: (response) => {
        this.purchaseLoading = false;
        
        const successMessage = `${response.detail} Выбрано игр: ${response.games_count}. Подписка действует до ${new Date(response.expires_at).toLocaleDateString('ru-RU')}.`;
        
        // Refresh user summary
        this.userSummaryService.refreshSummary();
        
        this.modalService.success('Покупка завершена!', successMessage).then(() => {
          this.enableBodyScroll();
          this.purchaseCompleted.emit();
          this.closeModal();
        });
      },
      error: (error) => {
        this.purchaseLoading = false;
        this.errorMessage = error.message || 'Ошибка при покупке пакета';
        
        // Handle specific error cases
        if (error.already_owned_games) {
          const ownedGames = error.already_owned_games.map((game: any) => game.title).join(', ');
          this.modalService.error('Игры уже в библиотеке', `У вас уже есть доступ к следующим играм: ${ownedGames}`);
        } else {
          this.modalService.error('Ошибка покупки', this.errorMessage);
        }
      }
    });
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  // Body scroll control methods
  private disableBodyScroll(): void {
    if (typeof document !== 'undefined') {
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = this.getScrollbarWidth() + 'px';
    }
  }

  private enableBodyScroll(): void {
    if (typeof document !== 'undefined') {
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    }
  }

  private getScrollbarWidth(): number {
    if (typeof window === 'undefined') return 0;
    
    const scrollDiv = document.createElement('div');
    scrollDiv.style.cssText = 'width: 100px; height: 100px; overflow: scroll; position: absolute; top: -9999px;';
    document.body.appendChild(scrollDiv);
    const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;
    document.body.removeChild(scrollDiv);
    return scrollbarWidth;
  }
}
