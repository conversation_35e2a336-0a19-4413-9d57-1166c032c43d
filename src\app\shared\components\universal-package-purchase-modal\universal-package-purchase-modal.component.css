/* Universal Package Purchase Modal Styles */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  z-index: 70;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content-container {
  width: 100%;
  max-width: 48rem;
  position: relative;
}

.modal-enter {
  animation: modalEnter 0.3s ease-out;
}

@keyframes modalEnter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Game Selection Styles */
.game-card {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.game-card:hover {
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.game-card.selected {
  border-color: rgba(59, 130, 246, 0.6);
  background: rgba(59, 130, 246, 0.1);
}

.game-image-container {
  height: 120px;
  position: relative;
  overflow: hidden;
}

.game-image {
  transition: transform 0.2s ease;
}

.game-card:hover .game-image {
  transform: scale(1.05);
}

.selection-overlay {
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.game-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Loading spinner */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Form validation styles */
.border-red-500\/50 {
  border-color: rgba(239, 68, 68, 0.5);
}

.focus\:ring-red-400:focus {
  --tw-ring-color: rgb(248 113 113);
}

.focus\:ring-blue-400:focus {
  --tw-ring-color: rgb(96 165 250);
}

.focus\:ring-green-400:focus {
  --tw-ring-color: rgb(74 222 128);
}

/* Success/Error message styles */
.info-message {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #93c5fd;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal-content-container {
    padding: 0.5rem;
  }

  .game-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Smaller game images on mobile */
  .game-image-container {
    height: 100px;
  }
}

@media (max-width: 1024px) {
  .game-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
