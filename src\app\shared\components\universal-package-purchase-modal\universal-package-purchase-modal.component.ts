import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../core/services/auth.service';
import { ModalService } from '../../../core/services/modal.service';
import { GamePackageService } from '../../../core/services/game-package.service';
import { UserSummaryService } from '../../../core/services/user-summary.service';
import { GamePackage, GamePackageGame, PackagePreviewResponse } from '../../../core/models/game-package.model';

type PurchaseStep = 'registration' | 'game-selection' | 'payment';

@Component({
  selector: 'app-universal-package-purchase-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './universal-package-purchase-modal.component.html',
  styleUrls: ['./universal-package-purchase-modal.component.css']
})
export class UniversalPackagePurchaseModalComponent implements OnInit, OnDestroy, OnChanges {
  @Input() isVisible = false;
  @Input() package: GamePackage | null = null;
  @Output() modalClosed = new EventEmitter<void>();
  @Output() purchaseCompleted = new EventEmitter<void>();

  // Step management
  currentStep: PurchaseStep = 'registration';
  
  // Registration form
  registrationForm: FormGroup;
  verificationForm: FormGroup;
  showCodeField = false;
  registeredEmail = '';
  registeredPassword = '';
  registrationCompleted = false;
  
  // Game selection
  selectedGameIds: number[] = [];
  availableGames: GamePackageGame[] = [];

  // Preview data
  previewData: PackagePreviewResponse | null = null;
  previewLoading = false;

  // Loading states
  isLoading = false;
  packageLoading = false;
  purchaseLoading = false;

  // Messages
  errorMessage = '';
  successMessage = '';

  private subscription?: Subscription;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private modalService: ModalService,
    private gamePackageService: GamePackageService,
    private userSummaryService: UserSummaryService
  ) {
    this.registrationForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      password_confirm: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    this.verificationForm = this.fb.group({
      code: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  ngOnInit(): void {
    this.initializeModal();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
    this.enableBodyScroll();
  }

  ngOnChanges(): void {
    if (this.isVisible && this.package) {
      this.initializeModal();
    }
  }

  private initializeModal(): void {
    if (!this.isVisible || !this.package) return;

    this.disableBodyScroll();
    this.resetModal();
    
    // Determine starting step based on authentication
    if (this.authService.isAuthenticated()) {
      this.currentStep = 'game-selection';
      this.registrationCompleted = true;
    } else {
      this.currentStep = 'registration';
      this.registrationCompleted = false;
    }

    // Load available games
    this.loadAvailableGames();
  }

  private resetModal(): void {
    this.showCodeField = false;
    this.registeredEmail = '';
    this.registeredPassword = '';
    this.selectedGameIds = [];
    this.previewData = null;
    this.previewLoading = false;
    this.isLoading = false;
    this.packageLoading = false;
    this.purchaseLoading = false;
    this.errorMessage = '';
    this.successMessage = '';
    this.registrationForm.reset();
    this.verificationForm.reset();
  }

  private loadAvailableGames(): void {
    if (!this.package?.games) return;
    this.availableGames = [...this.package.games];
  }

  // Password match validator
  private passwordMatchValidator(group: FormGroup) {
    const password = group.get('password');
    const confirmPassword = group.get('password_confirm');
    
    if (!password || !confirmPassword) {
      return null;
    }

    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  // Form validation helpers
  isFieldInvalid(fieldName: string, form: FormGroup = this.registrationForm): boolean {
    const field = form.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string, form: FormGroup = this.registrationForm): string {
    const field = form.get(fieldName);
    if (!field || !field.errors) return '';

    if (field.errors['required']) return 'Это поле обязательно';
    if (field.errors['email']) return 'Введите корректный email';
    if (field.errors['minlength']) return `Минимум ${field.errors['minlength'].requiredLength} символов`;
    if (field.errors['pattern']) return 'Введите 6-значный код';
    if (field.errors['passwordMismatch']) return 'Пароли не совпадают';

    return 'Некорректное значение';
  }

  // Body scroll management
  private disableBodyScroll(): void {
    document.body.style.overflow = 'hidden';
  }

  private enableBodyScroll(): void {
    document.body.style.overflow = '';
  }

  // Modal close
  closeModal(): void {
    this.enableBodyScroll();
    this.modalClosed.emit();
  }

  // Price formatting
  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  // Registration step methods
  onRegisterClick(): void {
    if (!this.registrationForm.valid) return;

    this.isLoading = true;
    this.errorMessage = '';

    const formData = this.registrationForm.value;
    this.registeredEmail = formData.email;
    this.registeredPassword = formData.password;

    this.authService.register({
      email: formData.email,
      password: formData.password
    }).subscribe({
      next: () => {
        this.isLoading = false;
        this.showCodeField = true;
        this.successMessage = 'Код подтверждения отправлен на ваш email';
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'Ошибка при регистрации';
      }
    });
  }

  onVerifyCode(): void {
    if (!this.verificationForm.valid) return;

    this.isLoading = true;
    this.errorMessage = '';

    this.authService.verifyCode({
      email: this.registeredEmail,
      code: this.verificationForm.value.code
    }).subscribe({
      next: () => {
        this.isLoading = false;
        this.registrationCompleted = true;
        
        // Auto-login after successful verification
        this.authService.login({
          email: this.registeredEmail,
          password: this.registeredPassword
        }).subscribe({
          next: () => {
            this.currentStep = 'game-selection';
            this.loadAvailableGames();
          },
          error: () => {
            this.errorMessage = 'Регистрация успешна, но не удалось войти автоматически';
          }
        });
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'Неверный код подтверждения';
      }
    });
  }

  resendCode(): void {
    if (!this.registeredEmail) return;

    this.isLoading = true;
    this.authService.register({
      email: this.registeredEmail,
      password: this.registeredPassword
    }).subscribe({
      next: () => {
        this.isLoading = false;
        this.successMessage = 'Код отправлен повторно';
      },
      error: () => {
        this.isLoading = false;
        this.errorMessage = 'Ошибка при отправке кода';
      }
    });
  }

  // Game selection methods
  onGameSelectionChange(gameId: number, event: any): void {
    const isChecked = event.target.checked;
    const maxSelectable = this.package?.max_selectable_games || 0;

    if (isChecked) {
      if (this.selectedGameIds.length < maxSelectable) {
        this.selectedGameIds.push(gameId);
      } else {
        event.target.checked = false;
        this.modalService.error('Превышен лимит', `Можно выбрать максимум ${maxSelectable} игр`);
        return;
      }
    } else {
      this.selectedGameIds = this.selectedGameIds.filter(id => id !== gameId);
    }

    // Update preview when selection changes
    this.updatePreview();
  }

  isGameSelected(gameId: number): boolean {
    return this.selectedGameIds.includes(gameId);
  }

  private updatePreview(): void {
    if (!this.package || this.selectedGameIds.length === 0) {
      this.previewData = null;
      return;
    }

    this.previewLoading = true;
    this.errorMessage = '';

    this.gamePackageService.previewPackagePurchase(this.package.id, this.selectedGameIds).subscribe({
      next: (response) => {
        this.previewLoading = false;
        this.previewData = response;

        // Show warnings if any
        if (response.preview.warnings && response.preview.warnings.length > 0) {
          this.modalService.alert('Предупреждение', response.preview.warnings.join('\n'));
        }
      },
      error: (error) => {
        this.previewLoading = false;
        this.errorMessage = error.message || 'Ошибка при получении предварительного просмотра';
        this.previewData = null;
      }
    });
  }

  onImageError(event: any): void {
    event.target.style.display = 'none';
  }

  // Navigation methods
  canGoBack(): boolean {
    if (this.currentStep === 'registration' && !this.registrationCompleted) {
      return false;
    }
    if (this.currentStep === 'game-selection' && this.registrationCompleted) {
      return false; // Can't go back to registration after completion
    }
    return this.currentStep === 'payment';
  }

  goBack(): void {
    if (this.currentStep === 'payment') {
      this.currentStep = 'game-selection';
    } else if (this.currentStep === 'game-selection' && !this.registrationCompleted) {
      this.currentStep = 'registration';
    }
  }

  canProceedToPayment(): boolean {
    return this.selectedGameIds.length > 0 && this.previewData?.can_purchase === true;
  }

  proceedToPayment(): void {
    if (this.canProceedToPayment()) {
      this.currentStep = 'payment';
    }
  }

  // Payment methods
  completePurchase(): void {
    if (!this.package || this.selectedGameIds.length === 0) {
      this.modalService.error('Ошибка', 'Не выбраны игры для покупки');
      return;
    }

    this.purchaseLoading = true;
    this.errorMessage = '';

    this.gamePackageService.purchasePackageWithGames(this.package.id, this.selectedGameIds).subscribe({
      next: (response) => {
        this.purchaseLoading = false;

        const successMessage = `${response.detail} Выбрано игр: ${response.games_count}. Подписка действует до ${new Date(response.expires_at).toLocaleDateString('ru-RU')}.`;

        // Refresh user summary
        this.userSummaryService.refreshSummary();

        this.modalService.success('Покупка завершена!', successMessage).then(() => {
          this.enableBodyScroll();
          this.purchaseCompleted.emit();
          this.closeModal();
        });
      },
      error: (error) => {
        this.purchaseLoading = false;
        this.errorMessage = error.message || 'Ошибка при покупке пакета';

        // Handle specific error cases
        if (error.already_owned_games) {
          const ownedGames = error.already_owned_games.map((game: any) => game.title).join(', ');
          this.modalService.error('Игры уже в библиотеке', `У вас уже есть доступ к следующим играм: ${ownedGames}`);
        } else {
          this.modalService.error('Ошибка покупки', this.errorMessage);
        }
      }
    });
  }

  getExpirationDate(): string {
    // Calculate expiration date based on package duration
    const now = new Date();
    const expirationDate = new Date(now.getTime() + (this.package?.duration_days || 30) * 24 * 60 * 60 * 1000);
    return expirationDate.toLocaleDateString('ru-RU');
  }
}
